"""
Redis-based call queue service for load-shedding and FIFO call management.

This service provides:
- FIFO call queuing using Redis Streams
- Active call counting with Redis counters
- Sub-10ms queue operations for high performance
- Tenant-isolated queues and limits
"""
import asyncio
import json
import logging
import os
import time
from dataclasses import dataclass, asdict
from typing import Optional, Dict, Any, List
from uuid import UUID

import redis.asyncio as redis
from redis.asyncio import Redis

logger = logging.getLogger(__name__)


@dataclass
class CallMeta:
    """Metadata for a queued call."""
    call_id: str
    tenant_id: str
    call_control_id: str
    telnyx_rtc_session_id: str
    lang: str = "en"
    phone_number: Optional[str] = None
    caller_name: Optional[str] = None
    enqueued_at: float = None
    
    def __post_init__(self):
        if self.enqueued_at is None:
            self.enqueued_at = time.time()


class QueueService:
    """Redis-based call queue service with sub-10ms operations."""
    
    def __init__(self, redis_url: Optional[str] = None):
        """
        Initialize the queue service.
        
        Args:
            redis_url: Redis connection URL. Defaults to REDIS_URL env var.
        """
        self.redis_url = redis_url or os.getenv("REDIS_URL", "redis://localhost:6379/0")
        self.redis: Optional[Redis] = None
        
        # Configuration from environment
        self.max_active_calls_per_tenant = int(
            os.getenv("MAX_ACTIVE_CALLS_PER_TENANT", "4")
        )
        self.max_queue_length_per_tenant = int(
            os.getenv("MAX_QUEUE_LENGTH_PER_TENANT", "10")
        )
        
        logger.info(
            f"Queue service initialized: max_active={self.max_active_calls_per_tenant}, "
            f"max_queue={self.max_queue_length_per_tenant}"
        )
    
    async def connect(self) -> None:
        """Establish Redis connection."""
        if self.redis is None:
            self.redis = redis.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=True,
                socket_connect_timeout=5,
                socket_keepalive=True,
                socket_keepalive_options={},
                health_check_interval=30
            )
            # Test connection
            await self.redis.ping()
            logger.info("Redis connection established")
    
    async def disconnect(self) -> None:
        """Close Redis connection."""
        if self.redis:
            await self.redis.close()
            self.redis = None
            logger.info("Redis connection closed")
    
    def _get_queue_key(self, tenant_id: str) -> str:
        """Get Redis stream key for tenant queue."""
        return f"voice:queue:{tenant_id}"
    
    def _get_active_key(self, tenant_id: str) -> str:
        """Get Redis counter key for active calls."""
        return f"voice:active:{tenant_id}"
    
    async def enqueue_call(self, tenant_id: str, call_id: str, ws_meta: Dict[str, Any]) -> bool:
        """
        Enqueue a call in the tenant's FIFO queue.
        
        Args:
            tenant_id: Tenant identifier
            call_id: Unique call identifier
            ws_meta: WebSocket metadata including call_control_id, telnyx_rtc_session_id, etc.
            
        Returns:
            True if successfully enqueued, False if queue is full
            
        Raises:
            redis.RedisError: If Redis operation fails
        """
        if not self.redis:
            await self.connect()
        
        queue_key = self._get_queue_key(tenant_id)
        
        try:
            # Check current queue length
            current_length = await self.redis.xlen(queue_key)
            if current_length >= self.max_queue_length_per_tenant:
                logger.warning(
                    f"Queue full for tenant {tenant_id}: {current_length}/{self.max_queue_length_per_tenant}"
                )
                return False
            
            # Create call metadata
            call_meta = CallMeta(
                call_id=call_id,
                tenant_id=tenant_id,
                call_control_id=ws_meta.get("call_control_id", ""),
                telnyx_rtc_session_id=ws_meta.get("telnyx_rtc_session_id", ""),
                lang=ws_meta.get("lang", "en"),
                phone_number=ws_meta.get("phone_number"),
                caller_name=ws_meta.get("caller_name")
            )
            
            # Add to Redis stream
            stream_data = {
                "call_meta": json.dumps(asdict(call_meta))
            }
            
            message_id = await self.redis.xadd(queue_key, stream_data)
            
            logger.info(
                f"Call {call_id} enqueued for tenant {tenant_id} "
                f"(queue_length={current_length + 1}, message_id={message_id})"
            )
            
            return True
            
        except redis.RedisError as e:
            logger.error(f"Failed to enqueue call {call_id} for tenant {tenant_id}: {e}")
            raise
    
    async def dequeue_next(self, tenant_id: str) -> Optional[CallMeta]:
        """
        Dequeue the next call from the tenant's queue (FIFO).
        
        Args:
            tenant_id: Tenant identifier
            
        Returns:
            CallMeta if a call was dequeued, None if queue is empty
            
        Raises:
            redis.RedisError: If Redis operation fails
        """
        if not self.redis:
            await self.connect()
        
        queue_key = self._get_queue_key(tenant_id)
        
        try:
            # Read oldest message from stream
            messages = await self.redis.xread({queue_key: "0-0"}, count=1, block=0)
            
            if not messages:
                return None
            
            stream_name, stream_messages = messages[0]
            if not stream_messages:
                return None
            
            message_id, fields = stream_messages[0]
            
            # Parse call metadata
            call_meta_json = fields.get("call_meta")
            if not call_meta_json:
                logger.error(f"Invalid message format in queue {queue_key}: {fields}")
                # Remove invalid message
                await self.redis.xdel(queue_key, message_id)
                return None
            
            call_meta_dict = json.loads(call_meta_json)
            call_meta = CallMeta(**call_meta_dict)
            
            # Remove message from stream
            await self.redis.xdel(queue_key, message_id)
            
            logger.info(
                f"Call {call_meta.call_id} dequeued for tenant {tenant_id} "
                f"(wait_time={time.time() - call_meta.enqueued_at:.2f}s)"
            )
            
            return call_meta
            
        except redis.RedisError as e:
            logger.error(f"Failed to dequeue call for tenant {tenant_id}: {e}")
            raise
        except (json.JSONDecodeError, TypeError) as e:
            logger.error(f"Failed to parse call metadata for tenant {tenant_id}: {e}")
            return None

    async def increment_active(self, tenant_id: str) -> int:
        """
        Increment active call counter for tenant.

        Args:
            tenant_id: Tenant identifier

        Returns:
            New active call count

        Raises:
            redis.RedisError: If Redis operation fails
        """
        if not self.redis:
            await self.connect()

        active_key = self._get_active_key(tenant_id)

        try:
            count = await self.redis.incr(active_key)
            # Set expiration to prevent memory leaks (24 hours)
            await self.redis.expire(active_key, 86400)

            logger.debug(f"Active calls for tenant {tenant_id}: {count}")
            return count

        except redis.RedisError as e:
            logger.error(f"Failed to increment active calls for tenant {tenant_id}: {e}")
            raise

    async def decrement_active(self, tenant_id: str) -> int:
        """
        Decrement active call counter for tenant.

        Args:
            tenant_id: Tenant identifier

        Returns:
            New active call count (minimum 0)

        Raises:
            redis.RedisError: If Redis operation fails
        """
        if not self.redis:
            await self.connect()

        active_key = self._get_active_key(tenant_id)

        try:
            count = await self.redis.decr(active_key)
            # Ensure count doesn't go below 0
            if count < 0:
                await self.redis.set(active_key, 0)
                count = 0

            logger.debug(f"Active calls for tenant {tenant_id}: {count}")
            return count

        except redis.RedisError as e:
            logger.error(f"Failed to decrement active calls for tenant {tenant_id}: {e}")
            raise

    async def get_active_count(self, tenant_id: str) -> int:
        """
        Get current active call count for tenant.

        Args:
            tenant_id: Tenant identifier

        Returns:
            Current active call count
        """
        if not self.redis:
            await self.connect()

        active_key = self._get_active_key(tenant_id)

        try:
            count = await self.redis.get(active_key)
            return int(count) if count else 0

        except redis.RedisError as e:
            logger.error(f"Failed to get active count for tenant {tenant_id}: {e}")
            return 0

    async def get_queue_length(self, tenant_id: str) -> int:
        """
        Get current queue length for tenant.

        Args:
            tenant_id: Tenant identifier

        Returns:
            Current queue length
        """
        if not self.redis:
            await self.connect()

        queue_key = self._get_queue_key(tenant_id)

        try:
            return await self.redis.xlen(queue_key)

        except redis.RedisError as e:
            logger.error(f"Failed to get queue length for tenant {tenant_id}: {e}")
            return 0

    async def get_queue_stats(self, tenant_id: str) -> Dict[str, Any]:
        """
        Get comprehensive queue statistics for tenant.

        Args:
            tenant_id: Tenant identifier

        Returns:
            Dictionary with queue statistics
        """
        try:
            active_count = await self.get_active_count(tenant_id)
            queue_length = await self.get_queue_length(tenant_id)

            return {
                "tenant_id": tenant_id,
                "active": active_count,
                "queued": queue_length,
                "max_active": self.max_active_calls_per_tenant,
                "max_queue": self.max_queue_length_per_tenant,
                "capacity_used": active_count / self.max_active_calls_per_tenant,
                "queue_used": queue_length / self.max_queue_length_per_tenant,
                "can_accept_calls": active_count < self.max_active_calls_per_tenant,
                "queue_available": queue_length < self.max_queue_length_per_tenant
            }

        except Exception as e:
            logger.error(f"Failed to get queue stats for tenant {tenant_id}: {e}")
            return {
                "tenant_id": tenant_id,
                "active": 0,
                "queued": 0,
                "max_active": self.max_active_calls_per_tenant,
                "max_queue": self.max_queue_length_per_tenant,
                "capacity_used": 0.0,
                "queue_used": 0.0,
                "can_accept_calls": True,
                "queue_available": True,
                "error": str(e)
            }

    async def clear_tenant_queue(self, tenant_id: str) -> int:
        """
        Clear all queued calls for a tenant (admin operation).

        Args:
            tenant_id: Tenant identifier

        Returns:
            Number of calls removed from queue
        """
        if not self.redis:
            await self.connect()

        queue_key = self._get_queue_key(tenant_id)

        try:
            # Get current length before clearing
            length = await self.redis.xlen(queue_key)

            # Delete the entire stream
            await self.redis.delete(queue_key)

            logger.warning(f"Cleared {length} calls from queue for tenant {tenant_id}")
            return length

        except redis.RedisError as e:
            logger.error(f"Failed to clear queue for tenant {tenant_id}: {e}")
            raise


# Global queue service instance
_queue_service: Optional[QueueService] = None


def get_queue_service() -> QueueService:
    """Get the global queue service instance."""
    global _queue_service
    if _queue_service is None:
        _queue_service = QueueService()
    return _queue_service
